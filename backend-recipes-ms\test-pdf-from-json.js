const fs = require('fs');
const path = require('path');

// Simple test script to generate PDF from JSON data
async function testPDFFromJSON() {
  try {
    console.log("🧪 Testing PDF generation from JSON data...");
    
    // Read the test data from JSON file
    const jsonPath = path.join(__dirname, 'test-recipe-data.json');
    const testData = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));
    
    console.log("📄 Loaded test data:");
    console.log(`- Recipe: ${testData.recipe_title}`);
    console.log(`- Ingredients: ${testData.ingredients.length} items`);
    console.log(`- Steps: ${testData.steps.length} steps`);
    console.log(`- Resources: ${testData.resources.length} resources`);
    console.log(`- HACCP attributes: ${testData.haccp_attributes.length} items`);
    
    // Try to import the PDF function
    let exportRecipeToPDF;
    try {
      // Try compiled version first
      const helper = require('./dist/helper/recipe.helper');
      exportRecipeToPDF = helper.exportRecipeToPDF;
      console.log("✅ Using compiled version");
    } catch (error) {
      console.log("⚠️ Compiled version not found, trying TypeScript...");
      try {
        // Try TypeScript version with ts-node
        require('ts-node/register');
        const helper = require('./src/helper/recipe.helper');
        exportRecipeToPDF = helper.exportRecipeToPDF;
        console.log("✅ Using TypeScript version");
      } catch (tsError) {
        console.error("❌ Could not load PDF function:", tsError.message);
        console.log("\n📝 To test the PDF generation:");
        console.log("1. Run 'npm run build' to compile TypeScript");
        console.log("2. Or run 'npm install ts-node' and try again");
        console.log("3. Or use the existing test endpoint in the API");
        return;
      }
    }
    
    // Generate PDF
    console.log("🔄 Generating PDF...");
    const pdfBuffer = await exportRecipeToPDF(testData);
    
    console.log("✅ PDF generated successfully!");
    console.log(`📄 PDF size: ${pdfBuffer.length} bytes`);
    
    // Save the PDF to a file
    const timestamp = new Date().toISOString().split("T")[0];
    const filename = `test_recipe_conditional_layout_${timestamp}.pdf`;
    
    fs.writeFileSync(filename, pdfBuffer);
    console.log(`💾 PDF saved as: ${filename}`);
    
    console.log("\n🎯 Test Results:");
    console.log("- The PDF should demonstrate the conditional layout system");
    console.log("- Page 1: Should have right-column content (Storage, Preparation, etc.)");
    console.log("- Page 2+: Should expand main content to full width (no empty right space)");
    console.log("- Check that METHOD, RESOURCES, CHEF TIPS sections use full page width");
    
    return pdfBuffer;
  } catch (error) {
    console.error("❌ Error generating test PDF:", error);
    throw error;
  }
}

// Alternative function to test with empty right column content
async function testPDFWithoutRightColumn() {
  try {
    console.log("\n🧪 Testing PDF with NO right-column content...");
    
    // Read the test data from JSON file
    const jsonPath = path.join(__dirname, 'test-recipe-data.json');
    const testData = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));
    
    // Remove right-column content to test full-width layout
    testData.haccp_attributes = []; // Empty HACCP
    // Note: Storage, Preparation, Cooking content is generated dynamically in the template
    
    console.log("📄 Modified test data to have NO right-column content");
    
    // Try to import the PDF function
    let exportRecipeToPDF;
    try {
      const helper = require('./dist/helper/recipe.helper');
      exportRecipeToPDF = helper.exportRecipeToPDF;
    } catch (error) {
      try {
        require('ts-node/register');
        const helper = require('./src/helper/recipe.helper');
        exportRecipeToPDF = helper.exportRecipeToPDF;
      } catch (tsError) {
        console.error("❌ Could not load PDF function:", tsError.message);
        return;
      }
    }
    
    // Generate PDF
    console.log("🔄 Generating PDF without right-column content...");
    const pdfBuffer = await exportRecipeToPDF(testData);
    
    console.log("✅ PDF generated successfully!");
    console.log(`📄 PDF size: ${pdfBuffer.length} bytes`);
    
    // Save the PDF to a file
    const timestamp = new Date().toISOString().split("T")[0];
    const filename = `test_recipe_full_width_${timestamp}.pdf`;
    
    fs.writeFileSync(filename, pdfBuffer);
    console.log(`💾 PDF saved as: ${filename}`);
    
    console.log("\n🎯 Test Results:");
    console.log("- This PDF should use FULL-WIDTH layout on all pages");
    console.log("- No empty white space on the right side");
    console.log("- All content should expand to use the full page width");
    
    return pdfBuffer;
  } catch (error) {
    console.error("❌ Error generating test PDF:", error);
    throw error;
  }
}

// Run the tests
console.log("🚀 Starting PDF Layout Tests...\n");

testPDFFromJSON()
  .then(() => {
    return testPDFWithoutRightColumn();
  })
  .then(() => {
    console.log("\n✅ All tests completed!");
    console.log("📁 Check the generated PDF files to verify the conditional layout system");
  })
  .catch(error => {
    console.error("\n❌ Test failed:", error);
  });
