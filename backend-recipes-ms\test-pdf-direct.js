const { exportRecipeToPDF } = require('./dist/helper/recipe.helper');

// Mock recipe data for testing
const mockRecipeData = {
  id: 123,
  recipe_title: "Mediterranean Chicken Soup",
  recipe_public_title: "Healthy Mediterranean Chicken",
  recipe_description: "A hearty and nutritious Mediterranean-style chicken soup packed with fresh vegetables, aromatic herbs, and tender chicken pieces. This recipe combines traditional Mediterranean flavors with modern cooking techniques to create a comforting meal perfect for any season.",
  recipe_preparation_time: 20,
  recipe_cook_time: 25,
  has_recipe_public_visibility: true,
  has_recipe_private_visibility: true,
  recipe_status: "publish",
  recipe_serve_in: "Individual bowls with crusty bread",
  recipe_complexity_level: "medium complexity",
  recipe_garnish: "Fresh herbs, lemon wedges, and olive oil drizzle",
  recipe_head_chef_tips: "Marinate chicken for at least 2 hours for best flavor",
  recipe_foh_tips: "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularized in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum",
  recipe_impression: "A warming, nutritious soup that brings the Mediterranean to your table",
  recipe_yield: 2500,
  recipe_yield_unit: "g",
  recipe_total_portions: 8,
  recipe_single_portion_size: 312,
  recipe_serving_method: "Hot in warmed bowls",
  recipe_slug: "mediterranean-chicken-soup",
  organization_id: "org-123",
  created_by: 1,
  updated_by: 1,
  created_at: "2024-01-15T10:30:00Z",
  updated_at: "2024-01-16T14:20:00Z",
  
  // User information
  creator_user_id: 1,
  creator_user_full_name: "Chef Maria Rodriguez",
  updater_user_id: 1,
  updater_user_full_name: "Chef Maria Rodriguez",
  
  // Recipe image
  item_detail: {
    item_id: 456,
    item_type: "image/jpeg",
    item_link: "https://images.unsplash.com/photo-1547592180-85f173990554?w=400"
  },
  
  // Categories
  categories: [
    {
      id: 1,
      category_name: "Soup",
      category_slug: "soup",
      category_status: "active",
      item_detail: {}
    }
  ],
  
  // Nutrition attributes
  nutrition_attributes: [
    {
      id: 1,
      attribute_title: "Calories",
      attribute_slug: "calories",
      attribute_type: "nutrition",
      unit: "285",
      unit_of_measure: "kcal",
      item_detail: {}
    },
    {
      id: 2,
      attribute_title: "Protein",
      attribute_slug: "protein",
      attribute_type: "nutrition",
      unit: "28",
      unit_of_measure: "g",
      item_detail: {}
    }
  ],
  
  // Allergen attributes
  allergen_attributes: {
    contains: [
      {
        id: 1,
        attribute_title: "Celery",
        attribute_slug: "celery",
        attribute_type: "allergen",
        may_contain: false,
        item_detail: {}
      }
    ],
    may_contain: [
      {
        id: 2,
        attribute_title: "Gluten",
        attribute_slug: "gluten",
        attribute_type: "allergen",
        may_contain: true,
        item_detail: {}
      }
    ]
  },
  
  // HACCP attributes
  haccp_attributes: [
    {
      id: 1,
      attribute_title: "Cook to appropriate temperature",
      attribute_slug: "cook-temp",
      attribute_type: "haccp_category",
      attribute_description: "Ensure chicken reaches internal temp of 165°F",
      item_detail: {}
    }
  ],
  
  // Ingredients
  ingredients: [
    {
      id: 1,
      ingredient_name: "Chicken breast",
      ingredient_slug: "chicken-breast",
      ingredient_description: "Fresh organic chicken breast",
      ingredient_status: "active",
      recipe_ingredient_status: "active",
      ingredient_quantity: 2,
      ingredient_measure: "kilogram",
      ingredient_wastage: 5,
      ingredient_cost: 6.50,
      ingredient_cooking_method: 1,
      ingredient_cooking_method_title: "Fresh organic chicken breast",
      preparation_method: 1,
      preparation_method_title: "Diced",
      measure_id: 1,
      measure_title: "kilogram",
      measure_slug: "kg"
    },
    {
      id: 2,
      ingredient_name: "Olive oil",
      ingredient_slug: "olive-oil",
      ingredient_description: "Extra virgin olive oil",
      ingredient_status: "active",
      recipe_ingredient_status: "active",
      ingredient_quantity: 50,
      ingredient_measure: "milliliter",
      ingredient_wastage: 0,
      ingredient_cost: 0.75,
      ingredient_cooking_method: null,
      ingredient_cooking_method_title: null,
      preparation_method: null,
      preparation_method_title: null,
      measure_id: 2,
      measure_title: "milliliter",
      measure_slug: "ml"
    }
  ],
  
  // Method steps
  steps: [
    {
      id: 1,
      recipe_step_order: 1,
      recipe_step_description: "Marinate chicken breasts in olive oil, lemon juice, and Mediterranean herbs.",
      item_id: 101,
      status: "active",
      item_detail: {
        item_id: 101,
        item_type: "image/jpeg",
        item_link: "https://images.unsplash.com/photo-1588166524941-3bf61a9c41db?w=300"
      }
    },
    {
      id: 2,
      recipe_step_order: 2,
      recipe_step_description: "Preheat grill to medium-high heat and oil the grates.",
      item_id: null,
      status: "active",
      item_detail: {}
    },
    {
      id: 3,
      recipe_step_order: 3,
      recipe_step_description: "Grill chicken for 6-7 minutes per side until internal temp reaches 165°F.",
      item_id: 102,
      status: "active",
      item_detail: {
        item_id: 102,
        item_type: "image/jpeg",
        item_link: "https://images.unsplash.com/photo-1532550907401-a500c9a57435?w=300"
      }
    }
  ],
  
  // Resources
  resources: [
    {
      id: 1,
      type: "item",
      status: "active",
      item_detail: {
        item_id: 201,
        item_type: "image/jpeg",
        item_link: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300"
      }
    },
    {
      id: 2,
      type: "item",
      status: "active",
      item_detail: {
        item_id: 202,
        item_type: "image/jpeg",
        item_link: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300"
      }
    },
    {
      id: 3,
      type: "link",
      status: "active",
      item_detail: {
        item_id: null,
        item_type: "video",
        item_link: "https://www.youtube.com/watch?v=example"
      }
    }
  ],
  
  // Assigned users
  assigned_users: [
    {
      user_id: 1,
      user_full_name: "Chef Maria Rodriguez",
      user_email: "<EMAIL>"
    }
  ]
};

async function testPDFGeneration() {
  try {
    console.log("🧪 Testing PDF generation with conditional layout...");
    
    // Add organization and user info for PDF generation
    const testData = {
      ...mockRecipeData,
      organization_name: "HL Org",
      creator_name: "Chef Maria Rodriguez"
    };
    
    // Generate PDF using the existing helper function
    const pdfBuffer = await exportRecipeToPDF(testData);
    
    console.log("✅ PDF generated successfully!");
    console.log(`📄 PDF size: ${pdfBuffer.length} bytes`);
    
    // Save the PDF to a file
    const fs = require('fs');
    const timestamp = new Date().toISOString().split("T")[0];
    const filename = `test_recipe_layout_${timestamp}.pdf`;
    
    fs.writeFileSync(filename, pdfBuffer);
    console.log(`💾 PDF saved as: ${filename}`);
    
    return pdfBuffer;
  } catch (error) {
    console.error("❌ Error generating test PDF:", error);
    throw error;
  }
}

// Run the test
testPDFGeneration().catch(console.error);
