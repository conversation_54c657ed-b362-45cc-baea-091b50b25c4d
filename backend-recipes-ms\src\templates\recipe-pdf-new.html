<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{RECIPE_TITLE}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.5;
            color: #1a1a1a;
            background: #ffffff;
            font-size: 10pt;
            margin: 0;
            padding: 0;
            font-weight: 400;
        }

        .container {
            max-width: 100%;
            margin: 0;
            padding: 0;
            background: white;
        }

        .main-content {
            width: 100%;
            padding: 0 8mm;
            padding-bottom: 5mm;
            background: #fefefe;
        }

        /* Clean professional header */
        .recipe-header {
            margin-bottom: 16pt;
            border-bottom: 1pt solid #2563eb;
            padding-bottom: 12pt;
        }

        .recipe-title-section {
            width: 100%;
        }

        .recipe-title {
            font-size: 18pt;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8pt;
            line-height: 1.3;
            letter-spacing: -0.025em;
        }

        .recipe-subtitle {
            font-size: 12pt;
            color: #64748b;
            font-weight: 400;
            margin-bottom: 6pt;
            line-height: 1.4;
        }

        .recipe-description {
            font-size: 10pt;
            color: #475569;
            line-height: 1.5;
            max-width: 85%;
        }

        /* Remove image container for cleaner look */
        .recipe-image-container {
            display: none;
        }

        /* Improved two column layout */
        .content-wrapper {
            display: flex;
            gap: 16pt;
            margin-top: 16pt;
        }

        .left-column {
            flex: 2;
        }

        .right-column {
            width: 140pt;
            flex-shrink: 0;
        }

        /* Professional info cards */
        .recipe-info-cards {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8pt;
            margin-bottom: 16pt;
        }

        .info-card {
            background: #f8fafc;
            border: 0.5pt solid #e2e8f0;
            border-radius: 4pt;
            padding: 8pt 6pt;
            text-align: center;
        }

        .info-card-label {
            font-size: 8pt;
            color: #64748b;
            margin-bottom: 4pt;
            text-transform: uppercase;
            font-weight: 500;
            letter-spacing: 0.025em;
        }

        .info-card-value {
            font-size: 11pt;
            font-weight: 600;
            color: #2563eb;
            line-height: 1.2;
        }

        /* Professional allergens section */
        .allergens-tags-section {
            background: #fef3c7;
            border: 0.5pt solid #f59e0b;
            border-radius: 4pt;
            padding: 8pt;
            margin-bottom: 16pt;
        }

        .allergens-tags-title {
            font-size: 9pt;
            font-weight: 600;
            color: #92400e;
            margin-bottom: 6pt;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .allergens-icons {
            display: flex;
            gap: 4pt;
            margin-bottom: 6pt;
        }

        .allergen-icon {
            width: 16pt;
            height: 16pt;
            background: #f59e0b;
            border-radius: 2pt;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8pt;
            color: white;
            font-weight: 600;
        }

        .tags-text {
            font-size: 9pt;
            color: #92400e;
            line-height: 1.4;
        }

        /* Professional method section */
        .method-section {
            margin-bottom: 16pt;
        }

        .method-step {
            margin-bottom: 12pt;
            padding: 10pt;
            background: #f0f9ff;
            border-left: 3pt solid #2563eb;
            border: 0.5pt solid #bfdbfe;
            border-radius: 4pt;
            font-size: 9pt;
            line-height: 1.5;
        }

        .step-header {
            margin-bottom: 6pt;
            font-weight: 600;
            color: #1e293b;
        }

        .step-content {
            display: flex;
            flex-direction: column;
            gap: 6pt;
        }

        .step-description {
            flex: 1;
            color: #374151;
        }

        /* Hide step images for cleaner professional look */
        .step-image-container {
            display: none;
        }

        /* Resources section - Enhanced background */
        .resources-section {
            margin-bottom: 8px;
            background: #fdf4ff;
            border: 0.5pt solid #a855f7;
            border-radius: 4pt;
            padding: 8pt;
        }

        .resource-card {
            margin-bottom: 8px;
            padding: 6px;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 11px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .resource-header {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            font-weight: bold;
        }

        .resource-icon {
            margin-right: 6px;
            font-size: 13px;
        }

        .resource-title {
            color: #135e96;
            font-size: 12px;
        }

        .resource-description {
            margin-bottom: 6px;
            font-size: 11px;
            color: #666;
            font-style: italic;
        }

        .resource-image {
            width: 100%;
            max-width: 120px;
            height: auto;
            max-height: 60px;
            object-fit: contain;
            border-radius: 3px;
            border: 1px solid #ddd;
            display: block;
            margin: 0 auto;
        }

        .resource-link {
            text-align: center;
            margin-top: 4px;
        }

        .resource-link a {
            color: #135e96 !important;
            text-decoration: none !important;
            font-weight: 600 !important;
            background: #e3f2fd;
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid #135e96;
            display: inline-block;
            font-size: 10px;
        }

        .image-fallback {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 12px;
            background: #f5f5f5;
            border-radius: 4px;
            border: 1px dashed #ccc;
            font-size: 11px;
        }

        /* New compact resource styles */
        .resources-container {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .resource-images {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 6px;
        }

        .resource-image-item {
            text-align: center;
        }

        .resource-image {
            width: 100%;
            max-width: 100px;
            height: auto;
            max-height: 60px;
            object-fit: contain;
            border-radius: 4px;
            border: 1px solid #ddd;
            display: block;
            margin: 0 auto;
        }

        .resource-links {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }

        .resource-link-item a {
            color: #135e96 !important;
            text-decoration: none !important;
            font-weight: 600 !important;
            background: #e3f2fd;
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid #135e96;
            display: inline-block;
            font-size: 11px;
        }

        /* Hidden class */
        .hidden {
            display: none !important;
        }

        /* HACCP items */
        .haccp-item {
            margin-bottom: 10px;
            padding: 8px;
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            font-size: 12px;
            line-height: 1.4;
        }

        /* Menu section */
        .menu-section {
            margin-bottom: 15px;
        }

        .menu-title {
            font-weight: bold;
            color: #135e96;
            margin-bottom: 10px;
            font-size: 18px;
        }

        .menu-list {
            font-size: 14px;
            line-height: 1.4;
        }

        /* Professional costs and yield sections - Enhanced backgrounds */
        .costs-yield-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12pt;
            margin-bottom: 16pt;
        }

        .cost-section {
            background: #f0fdf4;
            border: 0.5pt solid #22c55e;
            border-radius: 4pt;
            padding: 10pt;
        }

        .yield-section {
            background: #fef3c7;
            border: 0.5pt solid #f59e0b;
            border-radius: 4pt;
            padding: 10pt;
        }

        .section-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8pt;
            font-size: 11pt;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            border-bottom: 0.5pt solid #e2e8f0;
            padding-bottom: 4pt;
        }

        .cost-item,
        .yield-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4pt;
            font-size: 9pt;
            line-height: 1.4;
        }

        .cost-item:last-child,
        .yield-item:last-child {
            margin-bottom: 0;
        }

        /* Professional ingredients table */
        .ingredients-section {
            margin-bottom: 16pt;
        }

        .ingredients-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 9pt;
            border: 0.5pt solid #e2e8f0;
            border-radius: 4pt;
            overflow: hidden;
        }

        .ingredients-table th {
            background: #1e293b;
            color: white;
            padding: 8pt 6pt;
            text-align: left;
            font-weight: 600;
            font-size: 9pt;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .ingredients-table td {
            padding: 6pt;
            border-bottom: 0.5pt solid #e2e8f0;
            vertical-align: top;
            line-height: 1.4;
        }

        .ingredients-table tbody tr:nth-child(even) {
            background: #f8fafc;
        }

        .ingredients-table tbody tr:hover {
            background: #f1f5f9;
        }

        /* Professional right column sections - Optimized spacing */
        .haccp-section {
            background: #fef3c7;
            border: 0.5pt solid #f59e0b;
            border-radius: 4pt;
            padding: 6pt;
            margin-bottom: 4pt;
        }

        .storage-section,
        .preparation-section,
        .cooking-section {
            background: #eff6ff;
            border: 0.5pt solid #3b82f6;
            border-radius: 4pt;
            padding: 6pt;
            margin-bottom: 4pt;
        }

        .allergens-section {
            background: #fef3c7;
            border: 0.5pt solid #f59e0b;
            border-radius: 4pt;
            padding: 6pt;
            margin-bottom: 4pt;
        }

        .section-header {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4pt;
            font-size: 9pt;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .section-content {
            font-size: 8pt;
            line-height: 1.4;
            color: #374151;
        }

        .haccp-item {
            margin-bottom: 4px;
            padding: 4px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 3px;
            border-left: 2px solid #ffc107;
        }

        .haccp-item strong {
            color: #135e96;
            display: block;
            margin-bottom: 2px;
        }

        .allergen-item {
            display: flex;
            align-items: center;
            margin-bottom: 3px;
            font-size: 11px;
        }

        .allergen-icon {
            width: 16px;
            height: 16px;
            margin-right: 6px;
            border-radius: 3px;
        }

        /* Footer - Handled by Puppeteer, hide in HTML */
        .footer {
            display: none;
        }

        .footer-logo {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .page-info {
            text-align: right;
        }

        /* Professional nutrition section */
        .nutrition-section {
            margin-bottom: 16pt;
        }

        .nutrition-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 8pt;
            border: 0.5pt solid #e2e8f0;
            border-radius: 4pt;
            overflow: hidden;
        }

        .nutrition-table th {
            background: #1e293b;
            color: white;
            padding: 6pt 4pt;
            text-align: center;
            font-weight: 600;
            font-size: 8pt;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .nutrition-table td {
            padding: 4pt;
            border-bottom: 0.5pt solid #e2e8f0;
            text-align: center;
            font-size: 8pt;
            line-height: 1.4;
        }

        .nutrition-table tbody tr:nth-child(even) {
            background: #f8fafc;
        }

        .tips-section {
            margin-bottom: 16pt;
            page-break-inside: avoid;
        }

        .tips-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8pt;
        }

        .tip-card {
            background: #fefce8;
            border: 0.5pt solid #eab308;
            border-radius: 4pt;
            padding: 8pt;
            page-break-inside: avoid;
        }

        .tip-header {
            display: flex;
            align-items: center;
            margin-bottom: 4pt;
        }

        .tip-icon {
            margin-right: 4pt;
            font-size: 10pt;
        }

        .tip-title {
            font-weight: 600;
            color: #1e293b;
            font-size: 9pt;
        }

        .tip-content {
            font-size: 8pt;
            line-height: 1.5;
            color: #374151;
        }

        .serving-section {
            background: #f0fdf4;
            border: 0.5pt solid #16a34a;
            border-radius: 4pt;
            padding: 10pt;
            margin-bottom: 16pt;
            page-break-inside: avoid;
        }

        .serving-content {
            font-size: 9pt;
            line-height: 1.5;
            color: #374151;
        }

        /* Prevent premature page breaks */
        .method-section,
        .resources-section,
        .tips-section,
        .serving-section {
            page-break-inside: avoid;
            break-inside: avoid;
        }

        .ingredients-section {
            page-break-inside: auto;
            /* Allow ingredients table to break if needed */
        }

        /* Ensure content flows naturally on page 1 */
        .left-column {
            page-break-inside: auto;
        }

        .right-column {
            page-break-inside: auto;
        }

        /* Page layout - margins handled by Puppeteer */
        @page {
            size: A4;
        }

        /* Dynamic page breaks */
        .page-break-before {
            page-break-before: always;
        }

        .page-break-after {
            page-break-after: always;
        }

        /* Avoid breaking inside these elements */
        .avoid-break {
            page-break-inside: avoid;
            break-inside: avoid;
        }

        /* Print optimizations for dynamic content flow */
        @media print {
            body {
                margin: 0;
                padding: 0;
            }

            .container {
                padding: 0;
                margin: 0;
            }

            .main-content {
                padding: 0 5mm;
                padding-bottom: 0;
                /* Remove bottom padding as footer is handled by @page */
            }

            /* Keep sections together when possible */
            .recipe-header,
            .recipe-info-cards,
            .allergens-tags-section,
            .cost-section,
            .yield-section,
            .tips-section,
            .serving-section,
            .method-section,
            .resources-section {
                page-break-inside: avoid;
                break-inside: avoid;
            }

            /* Allow ingredients table to break naturally across pages */
            .ingredients-table {
                page-break-inside: auto;
            }

            /* Footer hidden - handled by Puppeteer */
            .footer {
                display: none;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="main-content">
            <!-- Header with title and image -->
            <div class="recipe-header">
                <div class="recipe-title-section">
                    <h1 class="recipe-title">{{RECIPE_TITLE}}</h1>
                    <div class="recipe-subtitle {{RECIPE_SUBTITLE_HIDDEN}}">{{RECIPE_SUBTITLE}}</div>
                    <div class="recipe-description {{RECIPE_DESCRIPTION_HIDDEN}}">{{RECIPE_DESCRIPTION}}</div>
                </div>
                <div class="recipe-image-container {{RECIPE_IMAGE_HIDDEN}}">
                    <img src="{{RECIPE_IMAGE_URL}}" alt="{{RECIPE_TITLE}}" class="recipe-image"
                        onerror="this.style.display='none'">
                </div>
            </div>

            <!-- Recipe Info Cards -->
            <div class="recipe-info-cards">
                <div class="info-card">
                    <div class="info-card-label">Prep Time</div>
                    <div class="info-card-value">{{RECIPE_PREP_TIME}}</div>
                </div>
                <div class="info-card">
                    <div class="info-card-label">Cook Time</div>
                    <div class="info-card-value">{{RECIPE_COOK_TIME}}</div>
                </div>
                <div class="info-card">
                    <div class="info-card-label">Total Time</div>
                    <div class="info-card-value">{{RECIPE_TOTAL_TIME}}</div>
                </div>
                <div class="info-card">
                    <div class="info-card-label">Servings</div>
                    <div class="info-card-value">{{RECIPE_SERVINGS}}</div>
                </div>
            </div>

            <!-- Allergens and Tags -->
            <div class="allergens-tags-section">
                <div class="allergens-tags-title">Allergens & Dietary Information</div>
                <div class="allergens-icons">{{ALLERGEN_ICONS}}</div>
                <div class="tags-text">{{RECIPE_TAGS}}</div>
            </div>

            <div class="content-wrapper">
                <!-- Left Column -->
                <div class="left-column">

                    <!-- Costs and Yield -->
                    <div class="costs-yield-grid">
                        <div class="cost-section">
                            <div class="section-title">Costs</div>
                            <div class="cost-item">
                                <span>Batch cost</span>
                                <span>{{BATCH_COST}}</span>
                            </div>
                            <div class="cost-item">
                                <span>Serving cost</span>
                                <span>{{SERVING_COST}}</span>
                            </div>
                        </div>
                        <div class="yield-section">
                            <div class="section-title">Yield</div>
                            <div class="yield-item">
                                <span>Cooked weight</span>
                                <span>{{COOKED_WEIGHT}}</span>
                            </div>
                            <div class="yield-item">
                                <span>Serving size</span>
                                <span>{{SERVING_SIZE}}</span>
                            </div>
                            <div class="yield-item">
                                <span>Servings per batch</span>
                                <span>{{SERVINGS_PER_BATCH}}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Ingredients Table -->
                    <div class="ingredients-section">
                        <div class="section-title">Ingredients</div>
                        <table class="ingredients-table">
                            <thead>
                                <tr>
                                    <th style="width: 25%;">Ingredient</th>
                                    <th style="width: 15%;">Qty</th>
                                    <th style="width: 10%;">Cost</th>
                                    <th style="width: 50%;">Preparation Details</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{INGREDIENTS_LIST}}
                            </tbody>
                        </table>
                    </div>

                    <!-- Method Section -->
                    <div class="method-section">
                        <div class="section-title">Method</div>
                        <div class="method-content">{{METHOD_CONTENT}}</div>
                    </div>

                    <!-- Resources Section -->
                    <div class="resources-section {{RESOURCES_HIDDEN}}">
                        <div class="section-title">Resources</div>
                        <div class="resources-content">{{RESOURCES_LIST}}</div>
                    </div>

                    <!-- Chef Tips and FOH Notes - Move to page 1 -->
                    <div class="tips-section {{TIPS_HIDDEN}}">
                        <div class="section-title">Chef Tips & FOH Notes</div>
                        <div class="tips-grid">
                            <div class="tip-card {{CHEF_TIPS_HIDDEN}}">
                                <div class="tip-header">
                                    <span class="tip-icon">👨‍🍳</span>
                                    <span class="tip-title">Head Chef Notes</span>
                                </div>
                                <div class="tip-content">{{CHEF_TIPS}}</div>
                            </div>
                            <div class="tip-card {{FOH_TIPS_HIDDEN}}">
                                <div class="tip-header">
                                    <span class="tip-icon">🏨</span>
                                    <span class="tip-title">FOH Notes</span>
                                </div>
                                <div class="tip-content">{{FOH_TIPS}}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Serving Instructions -->
                    <div class="serving-section {{SERVING_HIDDEN}} avoid-break">
                        <div class="section-title">Serving Instructions</div>
                        <div class="serving-content">{{SERVING_INSTRUCTIONS}}</div>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="right-column">
                    <!-- HACCP Section -->
                    <div class="haccp-section {{HACCP_HIDDEN}} avoid-break">
                        <div class="section-header">HACCP</div>
                        <div class="section-content">{{HACCP_CONTENT}}</div>
                    </div>

                    <!-- Storage Section -->
                    <div class="storage-section avoid-break">
                        <div class="section-header">Storage</div>
                        <div class="section-content">{{STORAGE_CONTENT}}</div>
                    </div>

                    <!-- Preparation Section -->
                    <div class="preparation-section avoid-break">
                        <div class="section-header">Preparation</div>
                        <div class="section-content">{{PREPARATION_CONTENT}}</div>
                    </div>

                    <!-- Cooking Section -->
                    <div class="cooking-section avoid-break">
                        <div class="section-header">Cooking</div>
                        <div class="section-content">{{COOKING_CONTENT}}</div>
                    </div>

                    <!-- Allergens Section -->
                    <div class="allergens-section {{ALLERGENS_HIDDEN}} avoid-break">
                        <div class="section-header">Allergens</div>
                        <div class="section-content">{{ALLERGENS_CONTENT}}</div>
                    </div>
                </div>
            </div>

            <!-- Nutrition Section - Dynamic content that flows naturally -->
            <div class="nutrition-section {{NUTRITION_HIDDEN}} page-break-before">
                <div class="section-title">Detailed Nutrition Information</div>
                <div class="nutrition-grid">
                    <div class="nutrition-column">
                        <table class="nutrition-table">
                            <thead>
                                <tr>
                                    <th>Nutrient</th>
                                    <th>Per 100g</th>
                                    <th>Per Serving</th>
                                    <th>% Daily Value</th>
                                    <th>Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{NUTRITION_TABLE_ROWS}}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer - Will appear on every page automatically -->
        <div class="footer">
            <div class="footer-logo">
                <span>🍽️ {{ORGANIZATION_NAME}}</span>
            </div>
            <div class="page-info">
                <div>Printed on {{GENERATION_DATE}}</div>
                <div>Printed by: {{CREATOR_NAME}}</div>
                <div>Page <span class="page-number"></span> of <span class="total-pages"></span></div>
            </div>
        </div>

    </div>
</body>

</html>