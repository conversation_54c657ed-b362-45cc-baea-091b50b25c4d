<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conditional Layout Test</title>
    <style>
        /* Copy the relevant CSS from the PDF template */
        .content-wrapper {
            display: flex;
            gap: 16pt;
            margin-top: 16pt;
            border: 2px solid #ccc;
            padding: 20px;
            margin-bottom: 20px;
        }

        .left-column {
            flex: 2;
            background: #f0f8ff;
            padding: 10px;
        }

        .right-column {
            width: 140pt;
            flex-shrink: 0;
            background: #fff8f0;
            padding: 10px;
        }

        /* JavaScript-controlled layout classes */
        .content-wrapper.no-right-content .left-column {
            flex: 1;
            max-width: 100%;
            background: #f0fff0;
        }

        .content-wrapper.no-right-content .right-column {
            display: none;
        }

        .section-header {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .section-content {
            font-size: 14px;
            color: #666;
        }

        .hidden {
            display: none;
        }

        h2 {
            color: #333;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 5px;
        }

        .test-description {
            background: #e6f3ff;
            padding: 15px;
            border-left: 4px solid #2563eb;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>Conditional Layout Test</h1>
    
    <div class="test-description">
        <strong>Test Description:</strong> This page demonstrates the conditional layout system. 
        The first section has right-column content and should use two-column layout. 
        The second section has no right-column content and should expand to full width.
    </div>

    <h2>Scenario 1: WITH Right Column Content (Two-Column Layout)</h2>
    <div class="content-wrapper">
        <div class="left-column">
            <h3>Main Content (Left Column)</h3>
            <p>This is the main content area. It contains ingredients, method steps, resources, etc.</p>
            <p><strong>METHOD</strong></p>
            <p>Step 1: Marinate chicken breasts in olive oil, lemon juice, and Mediterranean herbs.</p>
            <p>Step 2: Preheat grill to medium-high heat and oil the grates.</p>
            <p>Step 3: Grill chicken for 6-7 minutes per side until internal temp reaches 165°F.</p>
        </div>
        
        <div class="right-column">
            <!-- HACCP Section -->
            <div class="haccp-section">
                <div class="section-header">HACCP</div>
                <div class="section-content">Cook to appropriate temperature. Ensure chicken reaches internal temp of 165°F</div>
            </div>

            <!-- Storage Section -->
            <div class="storage-section">
                <div class="section-header">Storage</div>
                <div class="section-content">Store at appropriate temperature. Keep covered and protected from contamination.</div>
            </div>

            <!-- Preparation Section -->
            <div class="preparation-section">
                <div class="section-header">Preparation</div>
                <div class="section-content">Follow standard food safety procedures during preparation.</div>
            </div>

            <!-- Cooking Section -->
            <div class="cooking-section">
                <div class="section-header">Cooking</div>
                <div class="section-content">Cook to appropriate temperature. Cooking time: 25 minutes.</div>
            </div>

            <!-- Allergens Section -->
            <div class="allergens-section">
                <div class="section-header">Allergens</div>
                <div class="section-content">Please check with kitchen staff for allergen information.</div>
            </div>
        </div>
    </div>

    <h2>Scenario 2: WITHOUT Right Column Content (Full-Width Layout)</h2>
    <div class="content-wrapper">
        <div class="left-column">
            <h3>Main Content (Should Expand to Full Width)</h3>
            <p>This is the main content area for subsequent pages that don't have right-column content.</p>
            <p><strong>RESOURCES</strong></p>
            <p>• Watch Video: Cooking Techniques</p>
            <p>• View PDF: Recipe Guide</p>
            <p><strong>CHEF TIPS & FOH NOTES</strong></p>
            <p><strong>Head Chef Notes:</strong> Marinate chicken for at least 2 hours for best flavor</p>
            <p><strong>FOH Notes:</strong> Lorem Ipsum is simply dummy text of the printing and typesetting industry...</p>
            <p><strong>SERVING INSTRUCTIONS</strong></p>
            <p>Garnish: Fresh herbs, lemon wedges, and olive oil drizzle</p>
        </div>
        
        <div class="right-column">
            <!-- HACCP Section - EMPTY -->
            <div class="haccp-section hidden">
                <div class="section-header">HACCP</div>
                <div class="section-content"></div>
            </div>

            <!-- Storage Section - EMPTY -->
            <div class="storage-section">
                <div class="section-header">Storage</div>
                <div class="section-content"></div>
            </div>

            <!-- Preparation Section - EMPTY -->
            <div class="preparation-section">
                <div class="section-header">Preparation</div>
                <div class="section-content"></div>
            </div>

            <!-- Cooking Section - EMPTY -->
            <div class="cooking-section">
                <div class="section-header">Cooking</div>
                <div class="section-content"></div>
            </div>

            <!-- Allergens Section - EMPTY -->
            <div class="allergens-section hidden">
                <div class="section-header">Allergens</div>
                <div class="section-content"></div>
            </div>
        </div>
    </div>

    <script>
        // Dynamic layout adjustment based on right column content
        document.addEventListener('DOMContentLoaded', function () {
            const contentWrappers = document.querySelectorAll('.content-wrapper');

            contentWrappers.forEach(function (wrapper) {
                const rightColumn = wrapper.querySelector('.right-column');
                if (!rightColumn) return;

                // Check if right column has any visible content
                const hasVisibleContent = checkRightColumnContent(rightColumn);

                // If no visible content in right column, use full width layout
                if (!hasVisibleContent) {
                    wrapper.classList.add('no-right-content');
                    console.log('Applied no-right-content class to wrapper');
                } else {
                    console.log('Right column has content, keeping two-column layout');
                }
            });
        });

        function checkRightColumnContent(rightColumn) {
            // Check HACCP section
            const haccpSection = rightColumn.querySelector('.haccp-section');
            const haccpContent = haccpSection && haccpSection.querySelector('.section-content');
            const hasHaccp = haccpSection && !haccpSection.classList.contains('hidden') &&
                haccpContent && haccpContent.textContent.trim() !== '';

            // Check Storage section
            const storageSection = rightColumn.querySelector('.storage-section');
            const storageContent = storageSection && storageSection.querySelector('.section-content');
            const hasStorage = storageContent && storageContent.textContent.trim() !== '';

            // Check Preparation section
            const preparationSection = rightColumn.querySelector('.preparation-section');
            const preparationContent = preparationSection && preparationSection.querySelector('.section-content');
            const hasPreparation = preparationContent && preparationContent.textContent.trim() !== '';

            // Check Cooking section
            const cookingSection = rightColumn.querySelector('.cooking-section');
            const cookingContent = cookingSection && cookingSection.querySelector('.section-content');
            const hasCooking = cookingContent && cookingContent.textContent.trim() !== '';

            // Check Allergens section
            const allergensSection = rightColumn.querySelector('.allergens-section');
            const allergensContent = allergensSection && allergensSection.querySelector('.section-content');
            const hasAllergens = allergensSection && !allergensSection.classList.contains('hidden') &&
                allergensContent && allergensContent.textContent.trim() !== '';

            console.log('Content check:', {
                hasHaccp,
                hasStorage,
                hasPreparation,
                hasCooking,
                hasAllergens
            });

            return hasHaccp || hasStorage || hasPreparation || hasCooking || hasAllergens;
        }
    </script>
</body>
</html>
